# https://futures.flytogarden.com/api/v1/private/order/create
# https://futures.greentreeone.com/api/v1/private/order/cancel_all
#
# MEXC Plan Order Types:
# 1. Long plan order: deposit="0.06", triggerType="2", side=1
# 2. Short plan order: deposit="", triggerType="1", side=4
#
# Usage examples:
# - emulator.place_long_plan_order(symbol="SUI_USDT", trigger_price=2.5, vol=3)
# - emulator.place_short_plan_order(symbol="SUI_USDT", trigger_price=1.5, vol=3)
# - config = PlanOrderConfig.create_long_config(deposit="0.10")
#   emulator.place_plan_order(symbol="SUI_USDT", trigger_price=2.5, vol=3, config=config)
# - emulator.get_open_orders()      # Get regular limit orders only
# - emulator.get_open_plan_orders() # Get plan orders only  
# - emulator.get_all_open_orders()  # Get both regular and plan orders combined

import hashlib
import json
import time
import uuid
from datetime import datetime, timedelta, timezone
from curl_cffi import requests
from enum import Enum
from dataclasses import dataclass
from typing import Optional


def md5(value):
    return hashlib.md5(value.encode("utf-8")).hexdigest()


def mexc_crypto(key, obj):
    date_now = str(int((datetime.now(timezone.utc) + timedelta(hours=8)).timestamp() * 1000))
    g = md5(key + date_now)[7:]
    s = json.dumps(obj, separators=(",", ":"))
    sign = md5(date_now + s + g)
    return {"time": date_now, "sign": sign}


class PlanOrderType(Enum):
    LONG = "long"
    SHORT = "short"


@dataclass
class PlanOrderConfig:
    deposit: str
    trigger_type: str
    side: int
    
    @classmethod
    def create_long_config(cls, deposit: str = "0.06") -> 'PlanOrderConfig':
        return cls(
            deposit=deposit,
            trigger_type="2",
            side=1,
        )
    
    @classmethod
    def create_short_config(cls) -> 'PlanOrderConfig':
        return cls(
            deposit="",
            trigger_type="1", 
            side=4,
        )


class MexcOrderEmulator:
    def __init__(self):
        self.key = "APP99c7420d5052a8bb598874895eefa05fe4e36ce7786cfb3cdd9917e786b9cb82"
        self.orders = {}

    def get_android_headers(self, signature=None, request_body=None):
        headers = {
            "Accept-Encoding": "gzip",
            "app-language": "zh-MY",
            "Authentication": self.key,
            "Authorization": self.key,
            "client": "Android",
            "client-version": "6.19.1",
            "Connection": "Keep-Alive",
            "Content-Type": "application/json; charset=utf-8",
            "device-brand": "UkVETUk=",
            "device-id": "ffffffff-8c26-e8c1-ffff-ffffefd7ee7a",
            "device-model": "UkVETUkgUmVkbWkgSzMwIFBybw==",
            "Host": "futures.365huo.xyz",
            "language": "zh-MY",
            "network": "V0lGSQ==",
            "platform": "android",
            "uid": "38774483",
            "service-provider": "46011",
            "timezone": "UTC+8",
            "timezone-login": "UTC+08:00",
            "type": "0",
            "user-agent": "Dalvik/2.1.0 (Linux; U; Android 12; Redmi K30 Pro Build/SKQ1.211006.001)",
            "uuid": "ffffffff-8c26-e8c1-ffff-ffffefd7ee7a",
            "version": "6.19.1",
        }

        if signature:
            headers["x-mxc-nonce"] = signature["time"]
            headers["x-mxc-sign"] = signature["sign"]

        if request_body is not None:
            minified_json = json.dumps(request_body, separators=(",", ":"))
            headers["Content-Length"] = str(len(minified_json.encode("utf-8")))

        return headers

    def create_order(self, symbol="SUI_USDT", price=1.5, vol=2, side=1, leverage=100):
        start_time = time.perf_counter()

        order_data = {
            "bboTypeNum": 0,
            "flashClose": False,
            "isFlashTrade": False,
            "marketCeiling": False,
            "nonce": str(uuid.uuid4()),
            "priceProtect": "0",
            "reduceOnly": False,
            "type": 1,
            "leverage": str(leverage),
            "openType": 2,
            "price": str(price),
            "side": side,
            "symbol": symbol,
            "vol": str(vol),
        }

        signature = mexc_crypto(self.key, order_data)
        headers = self.get_android_headers(signature, order_data)

        url = "https://futures.365huo.xyz/api/v1/private/order/create"

        print(f"[CREATE ORDER] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())), indent=2)}")
        print(f"Request Body: {json.dumps(order_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=order_data, timeout=30)
            response_data = response.json()

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CREATE ORDER] Duration: {duration_ms:.2f} ms")

            print(f"Response Status: {response.status_code}")
            print(f"Response: {json.dumps(response_data, indent=2)}")

            if response_data.get("success"):
                order_id = response_data["data"]["orderId"]
                self.orders[order_id] = {"orderId": order_id, "symbol": symbol, "price": price, "vol": vol, "side": side, "leverage": leverage, "state": 2, "createTime": int(time.time() * 1000)}

            return response_data

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CREATE ORDER] Duration: {duration_ms:.2f} ms (with error)")

            error_response = {"success": False, "error": str(e)}
            print(f"Error: {error_response}")
            return error_response

    def cancel_order(self, order_id):
        start_time = time.perf_counter()

        cancel_data = [order_id]
        signature = mexc_crypto(self.key, cancel_data)
        headers = self.get_android_headers(signature, cancel_data)

        url = "https://futures.365huo.xyz/api/v1/private/order/cancel"

        print(f"[CANCEL ORDER] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())), indent=2)}")
        print(f"Request Body: {json.dumps(cancel_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=cancel_data, timeout=30)

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CANCEL ORDER] Duration: {duration_ms:.2f} ms")

            print(f"Response Status: {response.status_code}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")

                if response_data.get("success") and order_id in self.orders:
                    del self.orders[order_id]

                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CANCEL ORDER] Duration: {duration_ms:.2f} ms (with error)")

            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response

    def cancel_all_orders(self):
        start_time = time.perf_counter()

        cancel_data = {}
        signature = mexc_crypto(self.key, cancel_data)
        headers = self.get_android_headers(signature, cancel_data)
        headers["Host"] = "futures.365huo.xyz"

        url = "https://futures.365huo.xyz/api/v1/private/order/cancel_all"

        print(f"[CANCEL ALL ORDERS] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())), indent=2)}")
        print(f"Request Body: {json.dumps(cancel_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=cancel_data, timeout=30)

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CANCEL ALL ORDERS] Duration: {duration_ms:.2f} ms")

            print(f"Response Status: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")

                if response_data.get("success"):
                    cancelled_count = len(self.orders)
                    self.orders.clear()
                    print(f"Cancelled {cancelled_count} orders locally")

                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CANCEL ALL ORDERS] Duration: {duration_ms:.2f} ms (with error)")

            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response

    def place_plan_order(self, symbol="SUI_USDT", trigger_price=2, vol=3, side=1, 
                        deposit="0.06", net_balance=69.601502, trigger_type="2", 
                        unit_quantity=6, config: Optional[PlanOrderConfig] = None):
        start_time = time.perf_counter()

        if config:
            deposit = config.deposit
            trigger_type = config.trigger_type
            side = config.side

        plan_order_data = {
            "deposit": str(deposit),
            "executeCycle": "3",
            "netBalance": str(net_balance),
            "nonce": str(uuid.uuid4()),
            "positionMode": "1",
            "priceProtect": "0",
            "reduceOnly": False,
            "trend": "1",
            "triggerPrice": str(trigger_price),
            "triggerType": str(trigger_type),
            "unitQuantity": str(unit_quantity),
            "leverage": "100",
            "openType": "2",
            "orderType": "5",
            "side": side,
            "symbol": symbol,
            "vol": str(vol),
        }

        signature = mexc_crypto(self.key, plan_order_data)
        headers = self.get_android_headers(signature, plan_order_data)

        url = "https://futures.365huo.xyz/api/v1/private/planorder/place"

        print(f"[PLACE PLAN ORDER] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())), indent=2)}")
        print(f"Request Body: {json.dumps(plan_order_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=plan_order_data, timeout=30)
            response_data = response.json()

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[PLACE PLAN ORDER] Duration: {duration_ms:.2f} ms")

            print(f"Response Status: {response.status_code}")
            print(f"Response: {json.dumps(response_data, indent=2)}")

            return response_data

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[PLACE PLAN ORDER] Duration: {duration_ms:.2f} ms (with error)")

            error_response = {"success": False, "error": str(e)}
            print(f"Error: {error_response}")
            return error_response

    def place_long_plan_order(
        self,
        symbol="SUI_USDT",
        trigger_price=2,
        vol=3,
        deposit="0.06",
        net_balance=69.601502,
        unit_quantity=6,
    ):
        config = PlanOrderConfig.create_long_config(deposit)
        return self.place_plan_order(
            symbol=symbol,
            trigger_price=trigger_price,
            vol=vol,
            net_balance=net_balance,
            unit_quantity=unit_quantity,
            config=config,
        )

    def place_short_plan_order(
        self,
        symbol="SUI_USDT",
        trigger_price=2,
        vol=3,
        net_balance=69.601502,
        unit_quantity=6,
    ):
        config = PlanOrderConfig.create_short_config()
        return self.place_plan_order(
            symbol=symbol,
            trigger_price=trigger_price,
            vol=vol,
            net_balance=net_balance,
            unit_quantity=unit_quantity,
            config=config,
        )

    def cancel_plan_order(self, order_id, symbol="SUI_USDT"):
        start_time = time.perf_counter()

        cancel_data = [{"orderId": str(order_id), "symbol": symbol}]
        signature = mexc_crypto(self.key, cancel_data)
        headers = self.get_android_headers(signature, cancel_data)

        url = "https://futures.365huo.xyz/api/v1/private/planorder/cancel"

        print(f"[CANCEL PLAN ORDER] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())), indent=2)}")
        print(f"Request Body: {json.dumps(cancel_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=cancel_data, timeout=30)

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CANCEL PLAN ORDER] Duration: {duration_ms:.2f} ms")

            print(f"Response Status: {response.status_code}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")
                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CANCEL PLAN ORDER] Duration: {duration_ms:.2f} ms (with error)")

            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response

    def cancel_all_plan_orders(self):
        start_time = time.perf_counter()

        cancel_data = {}
        signature = mexc_crypto(self.key, cancel_data)
        headers = self.get_android_headers(signature, cancel_data)
        headers["Host"] = "futures.365huo.xyz"

        url = "https://futures.365huo.xyz/api/v1/private/planorder/cancel_all"

        print(f"[CANCEL ALL PLAN ORDERS] Making POST request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())), indent=2)}")
        print(f"Request Body: {json.dumps(cancel_data, indent=2)}")

        try:
            response = requests.post(url, headers=headers, json=cancel_data, timeout=30)

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CANCEL ALL PLAN ORDERS] Duration: {duration_ms:.2f} ms")

            print(f"Response Status: {response.status_code}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")
                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[CANCEL ALL PLAN ORDERS] Duration: {duration_ms:.2f} ms (with error)")

            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response

    def get_open_plan_orders(self):
        start_time = time.perf_counter()

        headers = self.get_android_headers()

        url = "https://futures.365huo.xyz/api/v1/private/planorder/list/orders?page_num=1&page_size=200&states=1"

        print(f"[GET OPEN PLAN ORDERS] Making GET request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())), indent=2)}")

        try:
            response = requests.get(url, headers=headers, timeout=30)

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[GET OPEN PLAN ORDERS] Duration: {duration_ms:.2f} ms")

            print(f"Response Status: {response.status_code}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")
                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[GET OPEN PLAN ORDERS] Duration: {duration_ms:.2f} ms (with error)")

            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response

    def get_open_orders(self):
        start_time = time.perf_counter()

        headers = self.get_android_headers()

        url = "https://futures.365huo.xyz/api/v1/private/order/list/open_orders?page_num=1&page_size=200"

        print(f"[GET OPEN ORDERS] Making GET request to {url}")
        print(f"Headers: {json.dumps(dict(list(headers.items())), indent=2)}")

        try:
            response = requests.get(url, headers=headers, timeout=30)

            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[GET OPEN ORDERS] Duration: {duration_ms:.2f} ms")

            print(f"Response Status: {response.status_code}")
            print(f"Raw Response Content: {response.text[:500]}...")

            try:
                response_data = response.json()
                print(f"Parsed JSON Response: {json.dumps(response_data, indent=2)}")
                return response_data

            except json.JSONDecodeError as json_error:
                error_response = {"success": False, "error": f"JSON decode error: {str(json_error)}", "raw_content": response.text, "status_code": response.status_code}
                print(f"JSON Parse Error: {error_response}")
                return error_response

        except Exception as e:
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            print(f"[GET OPEN ORDERS] Duration: {duration_ms:.2f} ms (with error)")

            error_response = {"success": False, "error": str(e)}
            print(f"Request Error: {error_response}")
            return error_response

    def get_all_open_orders(self):
        """Get both regular open orders and plan orders combined"""
        start_time = time.perf_counter()
        
        print(f"[GET ALL OPEN ORDERS] Fetching both regular orders and plan orders...")
        
        # Fetch regular orders
        regular_orders = self.get_open_orders()
        
        # Fetch plan orders  
        plan_orders = self.get_open_plan_orders()
        
        end_time = time.perf_counter()
        duration_ms = (end_time - start_time) * 1000
        print(f"[GET ALL OPEN ORDERS] Total Duration: {duration_ms:.2f} ms")
        
        # Combine results
        combined_response = {
            "success": True,
            "data": {
                "regular_orders": regular_orders,
                "plan_orders": plan_orders
            },
            "timestamp": int(time.time() * 1000)
        }
        
        # Check if either request failed
        if not regular_orders.get("success", False) or not plan_orders.get("success", False):
            combined_response["success"] = False
            combined_response["errors"] = {
                "regular_orders_error": None if regular_orders.get("success", False) else regular_orders.get("error"),
                "plan_orders_error": None if plan_orders.get("success", False) else plan_orders.get("error")
            }
        
        print(f"[GET ALL OPEN ORDERS] Combined response structure prepared")
        return combined_response


def test_scenario_1():
    print("=" * 60)
    print("TESTING SCENARIO 1: Create Order -> Cancel Order")
    print("=" * 60)

    emulator = MexcOrderEmulator()

    create_response = emulator.create_order(symbol="SUI_USDT", price=1.5, vol=2)
    if create_response.get("success") and "data" in create_response:
        order_id = create_response["data"]["orderId"]
    else:
        print("Failed to create order, exiting scenario")
        return

    emulator.cancel_order(order_id)

    emulator.get_open_orders()

    print("\nScenario 1 completed!")


def test_scenario_2():
    print("=" * 60)
    print("TESTING SCENARIO 2: Create Order -> Cancel All Orders")
    print("=" * 60)

    emulator = MexcOrderEmulator()

    print("\n1. Creating order...")
    create_response = emulator.create_order(symbol="SUI_USDT", price=100, vol=2)
    if create_response.get("success") and "data" in create_response:
        order_id = create_response["data"]["orderId"]
    else:
        print("Failed to create order, exiting scenario")
        return

    emulator.get_open_orders()

    emulator.cancel_all_orders()

    emulator.get_open_orders()

    print("\nScenario 2 completed!")


def test_scenario_3():
    print("=" * 60)
    print("TESTING SCENARIO 3: Individual Plan Order Cancellation + Cancel All")
    print("=" * 60)

    emulator = MexcOrderEmulator()
    symbol = "SUI_USDT"

    print("\n1. Placing long plan order...")
    print("   → Making HTTP REST API call for long plan order...")
    api_start_time = time.perf_counter()
    long_response = emulator.place_long_plan_order(
        symbol=symbol,
        trigger_price=2.5,
        vol=3,
    )
    api_end_time = time.perf_counter()
    api_duration_ms = (api_end_time - api_start_time) * 1000
    print(f"   → [API CALL 1] Long plan order total time: {api_duration_ms:.2f} ms")
    print(f"Long plan order response: {json.dumps(long_response, indent=2)}")
    
    # Extract order ID from long plan order if successful
    long_order_id = None
    if long_response.get("success") and "data" in long_response:
        # Plan order API returns order ID directly in data field
        long_order_id = long_response["data"]
        print(f"Long plan order ID: {long_order_id}")
    else:
        print("Failed to create long plan order, continuing anyway...")

    print("\n2. Canceling long plan order...")
    if long_order_id:
        print(f"   → Making HTTP REST API call to cancel long plan order {long_order_id}...")
        api_start_time = time.perf_counter()
        cancel_long_response = emulator.cancel_plan_order(long_order_id, symbol)
        api_end_time = time.perf_counter()
        api_duration_ms = (api_end_time - api_start_time) * 1000
        print(f"   → [API CALL 2] Cancel long plan order total time: {api_duration_ms:.2f} ms")
        print(f"Cancel long plan order response: {json.dumps(cancel_long_response, indent=2)}")
    else:
        print("   → Skipping long plan order cancellation (no order ID)")

    print("\n3. Placing short plan order...")
    print("   → Making HTTP REST API call for short plan order...")
    api_start_time = time.perf_counter()
    short_response = emulator.place_short_plan_order(
        symbol=symbol,
        trigger_price=6.0,
        vol=9,
    )
    api_end_time = time.perf_counter()
    api_duration_ms = (api_end_time - api_start_time) * 1000
    print(f"   → [API CALL 3] Short plan order total time: {api_duration_ms:.2f} ms")
    print(f"Short plan order response: {json.dumps(short_response, indent=2)}")
    
    # Extract order ID from short plan order if successful
    short_order_id = None
    if short_response.get("success") and "data" in short_response:
        # Plan order API returns order ID directly in data field
        short_order_id = short_response["data"]
        print(f"Short plan order ID: {short_order_id}")
    else:
        print("Failed to create short plan order, continuing anyway...")

    print("\n4. Canceling short plan order...")
    if short_order_id:
        print(f"   → Making HTTP REST API call to cancel short plan order {short_order_id}...")
        api_start_time = time.perf_counter()
        cancel_short_response = emulator.cancel_plan_order(short_order_id, symbol)
        api_end_time = time.perf_counter()
        api_duration_ms = (api_end_time - api_start_time) * 1000
        print(f"   → [API CALL 4] Cancel short plan order total time: {api_duration_ms:.2f} ms")
        print(f"Cancel short plan order response: {json.dumps(cancel_short_response, indent=2)}")
    else:
        print("   → Skipping short plan order cancellation (no order ID)")

    print("\n5. Canceling all remaining plan orders...")
    print("   → Making HTTP REST API call to cancel all plan orders...")
    api_start_time = time.perf_counter()
    cancel_all_response = emulator.cancel_all_plan_orders()
    api_end_time = time.perf_counter()
    api_duration_ms = (api_end_time - api_start_time) * 1000
    print(f"   → [API CALL 5] Cancel all plan orders total time: {api_duration_ms:.2f} ms")
    print(f"Cancel all plan orders response: {json.dumps(cancel_all_response, indent=2)}")

    print("\nScenario 3 completed!")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "1":
            test_scenario_1()
        elif sys.argv[1] == "2":
            test_scenario_2()
        elif sys.argv[1] == "3":
            test_scenario_3()
        else:
            print("Usage: python orders.py [1|2|3|")
            print("1 - Test scenario 1 (create -> cancel specific)")
            print("2 - Test scenario 2 (create -> cancel all)")
            print("3 - Test scenario 3 (place plan order)")
    else:
        print("Available test scenarios:")
        print("python orders.py 1  - Create order, cancel order")
        print("python orders.py 2  - Create order, cancel all orders")
        print("python orders.py 3  - Place plan order")
        print("\nQuick test (no sleep):")
        emulator = MexcOrderEmulator()
        print("\nDemonstrating MD5 signature generation:")
        test_data = {"symbol": "SUI_USDT", "price": "100", "vol": "2"}
        signature = mexc_crypto(emulator.key, test_data)
        print(f"Key: {emulator.key}")
        print(f"Data: {json.dumps(test_data)}")
        print(f"Generated signature: {signature}")
        print(f"MD5 hash example: {md5('test_string')}")
        print("\nCreating test order...")
        response = emulator.create_order()
        print("\nGetting open orders...")
        emulator.get_open_orders()
