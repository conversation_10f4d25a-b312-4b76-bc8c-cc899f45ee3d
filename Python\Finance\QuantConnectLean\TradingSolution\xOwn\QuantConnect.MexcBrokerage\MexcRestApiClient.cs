using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using QuantConnect.Brokerages;
using QuantConnect.Brokerages.Mexc.Messages;
using QuantConnect.Interfaces;
using QuantConnect.Logging;
using QuantConnect.Securities;
using QuantConnect.Util;
using RestSharp;

namespace QuantConnect.Brokerages.Mexc {
  public class MexcRestApiClient {
    private readonly ISymbolMapper _symbolMapper;
    private readonly ISecurityProvider _securityProvider;
    private readonly string _apiKey;
    private readonly string _apiSecret;
    private readonly string _restApiUrl;
    private readonly RateGate _restRateLimiter;
    private readonly RestClient _restClient;

    public MexcRestApiClient(
      ISymbolMapper symbolMapper,
      ISecurityProvider securityProvider,
      string apiKey,
      string apiSecret,
      string restApiUrl,
      RateGate restRateLimiter) {
      _symbolMapper = symbolMapper;
      _securityProvider = securityProvider;
      _apiKey = apiKey;
      _apiSecret = apiSecret;
      _restApiUrl = restApiUrl;
      _restRateLimiter = restRateLimiter;
      _restClient = new RestClient(_restApiUrl);
    }

    public MexcAccountAsset[] GetAccountAssets() {
      Log.Trace("MexcRestApiClient.GetAccountAssets(): Starting account assets request");

      var request = new RestRequest("/api/v1/private/account/assets", Method.GET);
      var response = ExecuteRestRequestWithSignature(request, "GetAccountAssets");

      Log.Trace($"MexcRestApiClient.GetAccountAssets(): Response status: {response.StatusCode}, Content length: {response.Content?.Length ?? 0}");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetAccountAssets: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      Log.Trace($"MexcRestApiClient.GetAccountAssets(): Raw response content: {response.Content}");

      var apiResponse = JsonConvert.DeserializeObject<MexcApiResponse<MexcAccountAsset[]>>(response.Content);

      Log.Trace($"MexcRestApiClient.GetAccountAssets(): Parsed response - Success: {apiResponse.Success}, Code: {apiResponse.Code}, Message: {apiResponse.Message}");

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetAccountAssets: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var assets = apiResponse.Data ?? new MexcAccountAsset[0];
      Log.Trace($"MexcRestApiClient.GetAccountAssets(): Returning {assets.Length} assets");

      return assets;
    }

    public List<Holding> GetAccountHoldings() {
      Log.Trace("MexcRestApiClient.GetAccountHoldings(): Starting account holdings request");

      var request = new RestRequest("/api/v1/private/position/open_positions", Method.GET);
      var response = ExecuteRestRequestWithSignature(request, "GetAccountHoldings");

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Response status: {response.StatusCode}, Content length: {response.Content?.Length ?? 0}");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetAccountHoldings: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Raw response content: {response.Content}");

      var apiResponse = JsonConvert.DeserializeObject<MexcPositionResponse>(response.Content);

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Parsed response - Success: {apiResponse.Success}, Code: {apiResponse.Code}, Message: {apiResponse.Message}");

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetAccountHoldings: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var positions = apiResponse.Data ?? new MexcPosition[0];
      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Found {positions.Length} positions");

      var holdings = new List<Holding>();

      foreach (var position in positions) {
        if (position.HoldVol != 0 && position.State == 1) {
          try {
            var symbol = _symbolMapper.GetLeanSymbol(position.Symbol, SecurityType.CryptoFuture, Market.MEXC);
            var quantity = position.PositionType == 1 ? position.HoldVol : -position.HoldVol;

            var holding = new Holding {
              Symbol = symbol,
              AveragePrice = position.HoldAvgPrice,
              Quantity = quantity,
            };

            holdings.Add(holding);
            Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Added holding - Symbol: {symbol}, Quantity: {quantity}, AvgPrice: {position.HoldAvgPrice}");
          } catch (Exception ex) {
            Log.Error($"MexcRestApiClient.GetAccountHoldings(): Error processing position {position.Symbol}: {ex.Message}");
          }
        }
      }

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Returning {holdings.Count} holdings");
      return holdings;
    }

    private IRestResponse ExecuteRestRequestWithSignature(RestRequest request, string endpoint) {
      _restRateLimiter.WaitToProceed();

      var timestamp = GetTimestamp();
      var requestParam = "";

      if (request.Method == Method.GET && request.Parameters.Any(p => p.Type == ParameterType.QueryString)) {
        var queryParams = request.Parameters
                            .Where(p => p.Type == ParameterType.QueryString)
                            .OrderBy(p => p.Name)
                            .Select(p => $"{p.Name}={p.Value}")
                            .ToArray();
        requestParam = string.Join("&", queryParams);
      }

      var signatureString = _apiKey + timestamp + requestParam;
      var signature = CreateSignature(signatureString, _apiSecret);

      request.AddHeader("ApiKey", _apiKey);
      request.AddHeader("Request-Time", timestamp);
      request.AddHeader("Signature", signature);
      request.AddHeader("Content-Type", "application/json");

      Log.Trace($"MexcRestApiClient.{endpoint}: Executing request to {request.Resource}");

      var response = _restClient.Execute(request);

      Log.Trace($"MexcRestApiClient.{endpoint}: Response status: {response.StatusCode}, Content: {response.Content}");

      return response;
    }

    private string GetTimestamp() {
      return DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(CultureInfo.InvariantCulture);
    }

    private string CreateSignature(string message, string secret) {
      using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret))) {
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(message));
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
      }
    }
  }

  public class MexcApiResponse<T> {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public T Data { get; set; }
  }

  public class MexcAccountAsset {
    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("positionMargin")]
    public decimal PositionMargin { get; set; }

    [JsonProperty("frozenBalance")]
    public decimal FrozenBalance { get; set; }

    [JsonProperty("availableBalance")]
    public decimal AvailableBalance { get; set; }

    [JsonProperty("cashBalance")]
    public decimal CashBalance { get; set; }
  }

  public class MexcPositionResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcPosition[] Data { get; set; }
  }

  public class MexcPosition {
    [JsonProperty("positionId")]
    public long PositionId { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("holdVol")]
    public decimal HoldVol { get; set; }

    [JsonProperty("positionType")]
    public int PositionType { get; set; }

    [JsonProperty("openType")]
    public int OpenType { get; set; }

    [JsonProperty("state")]
    public int State { get; set; }

    [JsonProperty("frozenVol")]
    public decimal FrozenVol { get; set; }

    [JsonProperty("closeVol")]
    public decimal CloseVol { get; set; }

    [JsonProperty("holdAvgPrice")]
    public decimal HoldAvgPrice { get; set; }

    [JsonProperty("closeAvgPrice")]
    public decimal CloseAvgPrice { get; set; }

    [JsonProperty("openAvgPrice")]
    public decimal OpenAvgPrice { get; set; }

    [JsonProperty("liquidatePrice")]
    public decimal LiquidatePrice { get; set; }

    [JsonProperty("oim")]
    public decimal Oim { get; set; }

    [JsonProperty("adlLevel")]
    public int AdlLevel { get; set; }

    [JsonProperty("im")]
    public decimal Im { get; set; }

    [JsonProperty("holdFee")]
    public decimal HoldFee { get; set; }

    [JsonProperty("realised")]
    public decimal Realised { get; set; }

    [JsonProperty("leverage")]
    public int Leverage { get; set; }
  }
}
