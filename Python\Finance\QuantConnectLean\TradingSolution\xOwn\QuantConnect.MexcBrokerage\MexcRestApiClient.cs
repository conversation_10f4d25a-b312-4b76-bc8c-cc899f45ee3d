using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using QuantConnect.Brokerages;
using QuantConnect.Brokerages.Mexc.Messages;
using QuantConnect.Interfaces;
using QuantConnect.Logging;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Util;
using RestSharp;

namespace QuantConnect.Brokerages.Mexc {
  public class MexcRestApiClient {
    private readonly ISymbolMapper _symbolMapper;
    private readonly ISecurityProvider _securityProvider;
    private readonly string _apiKey;
    private readonly string _apiSecret;
    private readonly string _restApiUrl;
    private readonly RateGate _restRateLimiter;
    private readonly RestClient _restClient;

    public event EventHandler<BrokerageOrderIdChangedEvent> OrderIdChanged;
    public event EventHandler<OrderEvent> OrderStatusChanged;

    public MexcRestApiClient(
      ISymbolMapper symbolMapper,
      ISecurityProvider securityProvider,
      string apiKey,
      string apiSecret,
      string restApiUrl,
      RateGate restRateLimiter) {
      _symbolMapper = symbolMapper;
      _securityProvider = securityProvider;
      _apiKey = apiKey;
      _apiSecret = apiSecret;
      _restApiUrl = restApiUrl;
      _restRateLimiter = restRateLimiter;
      _restClient = new RestClient(_restApiUrl);
    }

    public MexcAccountAsset[] GetAccountAssets() {
      Log.Trace("MexcRestApiClient.GetAccountAssets(): Starting account assets request");

      var request = new RestRequest("/api/v1/private/account/assets", Method.GET);
      var response = ExecuteRestRequestWithSignature(request, "GetAccountAssets");

      Log.Trace($"MexcRestApiClient.GetAccountAssets(): Response status: {response.StatusCode}, Content length: {response.Content?.Length ?? 0}");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetAccountAssets: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      Log.Trace($"MexcRestApiClient.GetAccountAssets(): Raw response content: {response.Content}");

      var apiResponse = JsonConvert.DeserializeObject<MexcApiResponse<MexcAccountAsset[]>>(response.Content);

      Log.Trace($"MexcRestApiClient.GetAccountAssets(): Parsed response - Success: {apiResponse.Success}, Code: {apiResponse.Code}, Message: {apiResponse.Message}");

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetAccountAssets: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var assets = apiResponse.Data ?? new MexcAccountAsset[0];
      Log.Trace($"MexcRestApiClient.GetAccountAssets(): Returning {assets.Length} assets");

      return assets;
    }

    public List<Holding> GetAccountHoldings() {
      Log.Trace("MexcRestApiClient.GetAccountHoldings(): Starting account holdings request");

      var request = new RestRequest("/api/v1/private/position/open_positions", Method.GET);
      var response = ExecuteRestRequestWithSignature(request, "GetAccountHoldings");

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Response status: {response.StatusCode}, Content length: {response.Content?.Length ?? 0}");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetAccountHoldings: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Raw response content: {response.Content}");

      var apiResponse = JsonConvert.DeserializeObject<MexcPositionResponse>(response.Content);

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Parsed response - Success: {apiResponse.Success}, Code: {apiResponse.Code}, Message: {apiResponse.Message}");

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetAccountHoldings: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var positions = apiResponse.Data ?? new MexcPosition[0];
      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Found {positions.Length} positions");

      var holdings = new List<Holding>();

      foreach (var position in positions) {
        if (position.HoldVol != 0 && position.State == 1) {
          try {
            var symbol = _symbolMapper.GetLeanSymbol(position.Symbol, SecurityType.CryptoFuture, Market.MEXC);
            var quantity = position.PositionType == 1 ? position.HoldVol : -position.HoldVol;

            var holding = new Holding {
              Symbol = symbol,
              AveragePrice = position.HoldAvgPrice,
              Quantity = quantity,
            };

            holdings.Add(holding);
            Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Added holding - Symbol: {symbol}, Quantity: {quantity}, AvgPrice: {position.HoldAvgPrice}");
          } catch (Exception ex) {
            Log.Error($"MexcRestApiClient.GetAccountHoldings(): Error processing position {position.Symbol}: {ex.Message}");
          }
        }
      }

      Log.Trace($"MexcRestApiClient.GetAccountHoldings(): Returning {holdings.Count} holdings");
      return holdings;
    }

    public List<Order> GetOpenOrders() {
      Log.Trace("MexcRestApiClient.GetOpenOrders(): Starting open orders request");

      var request = new RestRequest("/api/v1/private/order/list/open_orders", Method.GET);
      request.AddParameter("page_num", 1, ParameterType.QueryString);
      request.AddParameter("page_size", 200, ParameterType.QueryString);

      var response = ExecuteRestRequestWithSignature(request, "GetOpenOrders");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetOpenOrders: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      var apiResponse = JsonConvert.DeserializeObject<MexcOrderListResponse>(response.Content);

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetOpenOrders: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var orders = new List<Order>();
      var mexcOrders = apiResponse.Data ?? new MexcOrder[0];

      foreach (var mexcOrder in mexcOrders) {
        try {
          var order = ConvertMexcOrderToLeanOrder(mexcOrder);
          if (order != null) {
            orders.Add(order);
          }
        } catch (Exception ex) {
          Log.Error($"MexcRestApiClient.GetOpenOrders(): Error converting order {mexcOrder.OrderId}: {ex.Message}");
        }
      }

      Log.Trace($"MexcRestApiClient.GetOpenOrders(): Returning {orders.Count} orders");
      return orders;
    }

    public List<Order> GetOpenPlanOrders() {
      Log.Trace("MexcRestApiClient.GetOpenPlanOrders(): Starting open plan orders request");

      var request = new RestRequest("/api/v1/private/planorder/list/orders", Method.GET);
      request.AddParameter("page_num", 1, ParameterType.QueryString);
      request.AddParameter("page_size", 200, ParameterType.QueryString);
      request.AddParameter("states", 1, ParameterType.QueryString);

      var response = ExecuteRestRequestWithSignature(request, "GetOpenPlanOrders");

      if (response.StatusCode != HttpStatusCode.OK) {
        throw new Exception($"MexcRestApiClient.GetOpenPlanOrders: request failed: [{(int)response.StatusCode}] {response.StatusDescription}, Content: {response.Content}, ErrorMessage: {response.ErrorMessage}");
      }

      var apiResponse = JsonConvert.DeserializeObject<MexcPlanOrderListResponse>(response.Content);

      if (!apiResponse.Success) {
        throw new Exception($"MexcRestApiClient.GetOpenPlanOrders: API error: Code={apiResponse.Code}, Message={apiResponse.Message}");
      }

      var orders = new List<Order>();
      var mexcPlanOrders = apiResponse.Data ?? new MexcPlanOrder[0];

      foreach (var mexcPlanOrder in mexcPlanOrders) {
        try {
          var order = ConvertMexcPlanOrderToLeanOrder(mexcPlanOrder);
          if (order != null) {
            orders.Add(order);
          }
        } catch (Exception ex) {
          Log.Error($"MexcRestApiClient.GetOpenPlanOrders(): Error converting plan order {mexcPlanOrder.Id}: {ex.Message}");
        }
      }

      Log.Trace($"MexcRestApiClient.GetOpenPlanOrders(): Returning {orders.Count} plan orders");
      return orders;
    }

    public bool PlaceOrder(Order order) {
      try {
        if (order is StopMarketOrder stopMarketOrder) {
          return PlacePlanOrder(stopMarketOrder);
        } else if (order is LimitOrder limitOrder) {
          return PlaceLimitOrder(limitOrder);
        } else {
          Log.Error($"MexcRestApiClient.PlaceOrder(): Unsupported order type: {order.GetType().Name}");
          return false;
        }
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.PlaceOrder(): Error placing order {order.Id}: {ex.Message}");
        return false;
      }
    }

    public bool CancelOrder(Order order) {
      try {
        if (order is StopMarketOrder) {
          return CancelPlanOrder(order);
        } else {
          return CancelLimitOrder(order);
        }
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.CancelOrder(): Error cancelling order {order.Id}: {ex.Message}");
        return false;
      }
    }

    private IRestResponse ExecuteRestRequestWithSignature(RestRequest request, string endpoint) {
      _restRateLimiter.WaitToProceed();

      var timestamp = GetTimestamp();
      var requestParam = "";

      if (request.Method == Method.GET && request.Parameters.Any(p => p.Type == ParameterType.QueryString)) {
        var queryParams = request.Parameters
                            .Where(p => p.Type == ParameterType.QueryString)
                            .OrderBy(p => p.Name)
                            .Select(p => $"{p.Name}={p.Value}")
                            .ToArray();
        requestParam = string.Join("&", queryParams);
      }

      var signatureString = _apiKey + timestamp + requestParam;
      var signature = CreateSignature(signatureString, _apiSecret);

      request.AddHeader("ApiKey", _apiKey);
      request.AddHeader("Request-Time", timestamp);
      request.AddHeader("Signature", signature);
      request.AddHeader("Content-Type", "application/json");

      Log.Trace($"MexcRestApiClient.{endpoint}: Executing request to {request.Resource}");

      var response = _restClient.Execute(request);

      Log.Trace($"MexcRestApiClient.{endpoint}: Response status: {response.StatusCode}, Content: {response.Content}");

      return response;
    }

    private string GetTimestamp() {
      return DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(CultureInfo.InvariantCulture);
    }

    private string CreateSignature(string message, string secret) {
      using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret))) {
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(message));
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
      }
    }

    private bool PlaceLimitOrder(LimitOrder order) {
      var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(order.Symbol);
      brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");

      var side = GetMexcOrderSide(order);
      var body = new Dictionary<string, object> {
        ["symbol"] = brokerageSymbol,
        ["price"] = order.LimitPrice.ToString(CultureInfo.InvariantCulture),
        ["vol"] = Math.Abs(order.Quantity).ToString(CultureInfo.InvariantCulture),
        ["side"] = side,
        ["type"] = 1,
        ["openType"] = 2,
        ["leverage"] = "100",
        ["reduceOnly"] = false
      };

      var request = new RestRequest("/api/v1/private/order/create", Method.POST);
      request.AddJsonBody(body);

      var response = ExecuteRestRequestWithSignature(request, "PlaceLimitOrder");

      if (response.StatusCode == HttpStatusCode.OK) {
        var orderResponse = JsonConvert.DeserializeObject<MexcOrderResponse>(response.Content);

        if (orderResponse.Success && !string.IsNullOrEmpty(orderResponse.Data?.OrderId)) {
          order.BrokerId.Add(orderResponse.Data.OrderId);
          OnOrderSubmit(orderResponse.Data.OrderId, order);
          return true;
        } else {
          Log.Error($"MexcRestApiClient.PlaceLimitOrder(): API error: Code={orderResponse.Code}, Message={orderResponse.Message}");
          return false;
        }
      }

      Log.Error($"MexcRestApiClient.PlaceLimitOrder(): HTTP error: {response.StatusCode} - {response.Content}");
      return false;
    }

    private bool PlacePlanOrder(StopMarketOrder order) {
      var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(order.Symbol);
      brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");

      var side = GetMexcPlanOrderSide(order);
      var triggerType = GetMexcTriggerType(order);

      var body = new Dictionary<string, object> {
        ["symbol"] = brokerageSymbol,
        ["triggerPrice"] = order.StopPrice.ToString(CultureInfo.InvariantCulture),
        ["vol"] = Math.Abs(order.Quantity).ToString(CultureInfo.InvariantCulture),
        ["side"] = side,
        ["triggerType"] = triggerType,
        ["orderType"] = 5,
        ["openType"] = 2,
        ["leverage"] = "100",
        ["positionMode"] = 1,
        ["trend"] = 1,
        ["executeCycle"] = 3,
        ["reduceOnly"] = false,
        ["deposit"] = order.Quantity > 0 ? "0.06" : ""
      };

      var request = new RestRequest("/api/v1/private/planorder/place", Method.POST);
      request.AddJsonBody(body);

      var response = ExecuteRestRequestWithSignature(request, "PlacePlanOrder");

      if (response.StatusCode == HttpStatusCode.OK) {
        var planOrderResponse = JsonConvert.DeserializeObject<MexcPlanOrderResponse>(response.Content);

        if (planOrderResponse.Success && !string.IsNullOrEmpty(planOrderResponse.Data)) {
          order.BrokerId.Add(planOrderResponse.Data);
          OnOrderSubmit(planOrderResponse.Data, order);
          return true;
        } else {
          Log.Error($"MexcRestApiClient.PlacePlanOrder(): API error: Code={planOrderResponse.Code}, Message={planOrderResponse.Message}");
          return false;
        }
      }

      Log.Error($"MexcRestApiClient.PlacePlanOrder(): HTTP error: {response.StatusCode} - {response.Content}");
      return false;
    }

    private bool CancelLimitOrder(Order order) {
      var body = new List<string>();
      foreach (var brokerId in order.BrokerId) {
        body.Add(brokerId);
      }

      var request = new RestRequest("/api/v1/private/order/cancel", Method.POST);
      request.AddJsonBody(body);

      var response = ExecuteRestRequestWithSignature(request, "CancelLimitOrder");

      if (response.StatusCode == HttpStatusCode.OK) {
        var cancelResponse = JsonConvert.DeserializeObject<MexcApiResponse<object>>(response.Content);
        return cancelResponse.Success;
      }

      Log.Error($"MexcRestApiClient.CancelLimitOrder(): HTTP error: {response.StatusCode} - {response.Content}");
      return false;
    }

    private bool CancelPlanOrder(Order order) {
      var brokerageSymbol = _symbolMapper.GetBrokerageSymbol(order.Symbol);
      brokerageSymbol = brokerageSymbol.Replace("USDT", "_USDT");

      var body = new List<Dictionary<string, object>>();
      foreach (var brokerId in order.BrokerId) {
        body.Add(new Dictionary<string, object> {
          ["orderId"] = brokerId,
          ["symbol"] = brokerageSymbol
        });
      }

      var request = new RestRequest("/api/v1/private/planorder/cancel", Method.POST);
      request.AddJsonBody(body);

      var response = ExecuteRestRequestWithSignature(request, "CancelPlanOrder");

      if (response.StatusCode == HttpStatusCode.OK) {
        var cancelResponse = JsonConvert.DeserializeObject<MexcApiResponse<object>>(response.Content);
        return cancelResponse.Success;
      }

      Log.Error($"MexcRestApiClient.CancelPlanOrder(): HTTP error: {response.StatusCode} - {response.Content}");
      return false;
    }

    private Order ConvertMexcOrderToLeanOrder(MexcOrder mexcOrder) {
      try {
        var brokerageSymbol = mexcOrder.Symbol.Replace("_USDT", "USDT");
        var symbol = _symbolMapper.GetLeanSymbol(brokerageSymbol, SecurityType.CryptoFuture, Market.MEXC);
        var quantity = GetLeanOrderQuantity(mexcOrder.Side, mexcOrder.Volume);
        var orderTime = Time.UnixMillisecondTimeStampToDateTime(mexcOrder.CreateTime);

        var order = new LimitOrder(symbol, quantity, mexcOrder.Price, orderTime);
        order.BrokerId.Add(mexcOrder.OrderId);
        order.Status = ConvertMexcOrderStatus(mexcOrder.State);

        return order;
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.ConvertMexcOrderToLeanOrder(): Error converting order: {ex.Message}");
        return null;
      }
    }

    private Order ConvertMexcPlanOrderToLeanOrder(MexcPlanOrder mexcPlanOrder) {
      try {
        var brokerageSymbol = mexcPlanOrder.Symbol.Replace("_USDT", "USDT");
        var symbol = _symbolMapper.GetLeanSymbol(brokerageSymbol, SecurityType.CryptoFuture, Market.MEXC);
        var quantity = GetLeanPlanOrderQuantity(mexcPlanOrder.Side, mexcPlanOrder.Volume);
        var orderTime = Time.UnixMillisecondTimeStampToDateTime(mexcPlanOrder.CreateTime);

        var order = new StopMarketOrder(symbol, quantity, mexcPlanOrder.TriggerPrice, orderTime);
        order.BrokerId.Add(mexcPlanOrder.Id);
        order.Status = ConvertMexcPlanOrderStatus(mexcPlanOrder.State);

        return order;
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.ConvertMexcPlanOrderToLeanOrder(): Error converting plan order: {ex.Message}");
        return null;
      }
    }

    private int GetMexcOrderSide(Order order) {
      return order.Quantity > 0 ? 1 : 3;
    }

    private int GetMexcPlanOrderSide(Order order) {
      return order.Quantity > 0 ? 1 : 4;
    }

    private int GetMexcTriggerType(StopMarketOrder order) {
      return order.Quantity > 0 ? 2 : 1;
    }

    private decimal GetLeanOrderQuantity(int mexcSide, decimal mexcVolume) {
      return mexcSide == 1 || mexcSide == 4 ? mexcVolume : -mexcVolume;
    }

    private decimal GetLeanPlanOrderQuantity(int mexcSide, decimal mexcVolume) {
      return mexcSide == 1 ? mexcVolume : -mexcVolume;
    }

    private OrderStatus ConvertMexcOrderStatus(int mexcState) {
      switch (mexcState) {
        case 1: return OrderStatus.New;
        case 2: return OrderStatus.PartiallyFilled;
        case 3: return OrderStatus.Filled;
        case 4: return OrderStatus.Canceled;
        case 5: return OrderStatus.Invalid;
        default: return OrderStatus.None;
      }
    }

    private OrderStatus ConvertMexcPlanOrderStatus(int mexcState) {
      switch (mexcState) {
        case 1: return OrderStatus.Submitted;
        case 2: return OrderStatus.Canceled;
        default: return OrderStatus.None;
      }
    }

    private void OnOrderSubmit(string brokerId, Order order) {
      try {
        OrderIdChanged?.Invoke(this, new BrokerageOrderIdChangedEvent { BrokerId = new List<string> { brokerId }, OrderId = order.Id });

        var orderEvent = new OrderEvent(
          order,
          DateTime.UtcNow,
          OrderFee.Zero,
          "MEXC Order Event") { Status = OrderStatus.Submitted };

        OrderStatusChanged?.Invoke(this, orderEvent);
        Log.Trace($"MexcRestApiClient.OnOrderSubmit(): Order submitted successfully - OrderId: {order.Id}, BrokerId: {brokerId}");
      } catch (Exception ex) {
        Log.Error($"MexcRestApiClient.OnOrderSubmit(): Error processing order submit: {ex.Message}");
      }
    }
  }

  public class MexcApiResponse<T> {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public T Data { get; set; }
  }

  public class MexcAccountAsset {
    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("positionMargin")]
    public decimal PositionMargin { get; set; }

    [JsonProperty("frozenBalance")]
    public decimal FrozenBalance { get; set; }

    [JsonProperty("availableBalance")]
    public decimal AvailableBalance { get; set; }

    [JsonProperty("cashBalance")]
    public decimal CashBalance { get; set; }
  }

  public class MexcPositionResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcPosition[] Data { get; set; }
  }

  public class MexcPosition {
    [JsonProperty("positionId")]
    public long PositionId { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("holdVol")]
    public decimal HoldVol { get; set; }

    [JsonProperty("positionType")]
    public int PositionType { get; set; }

    [JsonProperty("openType")]
    public int OpenType { get; set; }

    [JsonProperty("state")]
    public int State { get; set; }

    [JsonProperty("frozenVol")]
    public decimal FrozenVol { get; set; }

    [JsonProperty("closeVol")]
    public decimal CloseVol { get; set; }

    [JsonProperty("holdAvgPrice")]
    public decimal HoldAvgPrice { get; set; }

    [JsonProperty("closeAvgPrice")]
    public decimal CloseAvgPrice { get; set; }

    [JsonProperty("openAvgPrice")]
    public decimal OpenAvgPrice { get; set; }

    [JsonProperty("liquidatePrice")]
    public decimal LiquidatePrice { get; set; }

    [JsonProperty("oim")]
    public decimal Oim { get; set; }

    [JsonProperty("adlLevel")]
    public int AdlLevel { get; set; }

    [JsonProperty("im")]
    public decimal Im { get; set; }

    [JsonProperty("holdFee")]
    public decimal HoldFee { get; set; }

    [JsonProperty("realised")]
    public decimal Realised { get; set; }

    [JsonProperty("leverage")]
    public int Leverage { get; set; }
  }
}
