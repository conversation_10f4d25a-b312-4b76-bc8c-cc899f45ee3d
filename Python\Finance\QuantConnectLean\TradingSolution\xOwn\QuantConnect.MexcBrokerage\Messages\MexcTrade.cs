using Newtonsoft.Json;

namespace QuantConnect.Brokerages.Mexc.Messages {
  public class MexcTrade {
    [JsonProperty("p")]
    public decimal Price { get; set; }

    [JsonProperty("v")]
    public decimal Volume { get; set; }

    [JsonProperty("T")]
    public int Side { get; set; }

    [JsonProperty("O")]
    public int OpenPosition { get; set; }

    [JsonProperty("M")]
    public int SelfTransact { get; set; }

    [JsonProperty("t")]
    public long Timestamp { get; set; }
  }

  public class MexcTradeMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcTrade Data { get; set; }
  }

  public class MexcOrderDeal {
    [JsonProperty("category")]
    public int Category { get; set; }

    [JsonProperty("externalOid")]
    public string ExternalOid { get; set; }

    [JsonProperty("fee")]
    public decimal Fee { get; set; }

    [JsonProperty("feeCurrency")]
    public string FeeCurrency { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("isSelf")]
    public bool IsSelf { get; set; }

    [JsonProperty("orderId")]
    public string OrderId { get; set; }

    [JsonProperty("positionMode")]
    public int PositionMode { get; set; }

    [JsonProperty("price")]
    public decimal Price { get; set; }

    [JsonProperty("profit")]
    public decimal Profit { get; set; }

    [JsonProperty("side")]
    public int Side { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("taker")]
    public bool Taker { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("vol")]
    public decimal Volume { get; set; }
  }

  public class MexcOrderDealMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcOrderDeal Data { get; set; }
  }

  public class MexcOrder {
    [JsonProperty("orderId")]
    public string OrderId { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("positionId")]
    public long PositionId { get; set; }

    [JsonProperty("price")]
    public decimal Price { get; set; }

    [JsonProperty("vol")]
    public decimal Volume { get; set; }

    [JsonProperty("leverage")]
    public int Leverage { get; set; }

    [JsonProperty("side")]
    public int Side { get; set; }

    [JsonProperty("category")]
    public int Category { get; set; }

    [JsonProperty("orderType")]
    public int OrderType { get; set; }

    [JsonProperty("dealVol")]
    public decimal DealVolume { get; set; }

    [JsonProperty("dealAvgPrice")]
    public decimal DealAveragePrice { get; set; }

    [JsonProperty("state")]
    public int State { get; set; }

    [JsonProperty("createTime")]
    public long CreateTime { get; set; }

    [JsonProperty("updateTime")]
    public long UpdateTime { get; set; }

    [JsonProperty("externalOid")]
    public string ExternalOrderId { get; set; }

    [JsonProperty("errorCode")]
    public int ErrorCode { get; set; }

    [JsonProperty("usedMargin")]
    public decimal UsedMargin { get; set; }

    [JsonProperty("takerFeeRate")]
    public decimal TakerFeeRate { get; set; }

    [JsonProperty("makerFeeRate")]
    public decimal MakerFeeRate { get; set; }

    [JsonProperty("reduceOnly")]
    public bool ReduceOnly { get; set; }
  }

  public class MexcOrderResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcOrderResult Data { get; set; }
  }

  public class MexcOrderResult {
    [JsonProperty("orderId")]
    public string OrderId { get; set; }
  }

  public class MexcOrderListResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcOrder[] Data { get; set; }
  }

  public class MexcPlanOrder {
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("leverage")]
    public int Leverage { get; set; }

    [JsonProperty("side")]
    public int Side { get; set; }

    [JsonProperty("vol")]
    public decimal Volume { get; set; }

    [JsonProperty("openType")]
    public int OpenType { get; set; }

    [JsonProperty("orderType")]
    public int OrderType { get; set; }

    [JsonProperty("triggerPrice")]
    public decimal TriggerPrice { get; set; }

    [JsonProperty("triggerType")]
    public int TriggerType { get; set; }

    [JsonProperty("executeCycle")]
    public int ExecuteCycle { get; set; }

    [JsonProperty("trend")]
    public int Trend { get; set; }

    [JsonProperty("state")]
    public int State { get; set; }

    [JsonProperty("createTime")]
    public long CreateTime { get; set; }

    [JsonProperty("updateTime")]
    public long UpdateTime { get; set; }

    [JsonProperty("positionMode")]
    public int PositionMode { get; set; }

    [JsonProperty("reduceOnly")]
    public bool ReduceOnly { get; set; }

    [JsonProperty("lossTrend")]
    public int LossTrend { get; set; }

    [JsonProperty("profitTrend")]
    public int ProfitTrend { get; set; }
  }

  public class MexcPlanOrderMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcPlanOrder Data { get; set; }
  }

  public class MexcPlanOrderResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public string Data { get; set; }
  }

  public class MexcPlanOrderListResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcPlanOrder[] Data { get; set; }
  }
}
