using Newtonsoft.Json;

namespace QuantConnect.Brokerages.Mexc.Messages {
  public class MexcTrade {
    [JsonProperty("p")]
    public decimal Price { get; set; }

    [JsonProperty("v")]
    public decimal Volume { get; set; }

    [JsonProperty("T")]
    public int Side { get; set; }

    [JsonProperty("O")]
    public int OpenPosition { get; set; }

    [JsonProperty("M")]
    public int SelfTransact { get; set; }

    [JsonProperty("t")]
    public long Timestamp { get; set; }
  }

  public class MexcTradeMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcTrade Data { get; set; }
  }

  public class MexcOrderDeal {
    [JsonProperty("category")]
    public int Category { get; set; }

    [JsonProperty("externalOid")]
    public string ExternalOid { get; set; }

    [JsonProperty("fee")]
    public decimal Fee { get; set; }

    [JsonProperty("feeCurrency")]
    public string FeeCurrency { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("isSelf")]
    public bool IsSelf { get; set; }

    [JsonProperty("orderId")]
    public string OrderId { get; set; }

    [JsonProperty("positionMode")]
    public int PositionMode { get; set; }

    [JsonProperty("price")]
    public decimal Price { get; set; }

    [JsonProperty("profit")]
    public decimal Profit { get; set; }

    [JsonProperty("side")]
    public int Side { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("taker")]
    public bool Taker { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("vol")]
    public decimal Volume { get; set; }
  }

  public class MexcOrderDealMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcOrderDeal Data { get; set; }
  }
}
